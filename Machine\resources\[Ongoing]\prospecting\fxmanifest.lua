name 'Prospecting'
author 'glitchdetector'
contact '<EMAIL>'

fx_version 'adamant'
game 'gta5'

lua54 'yes'

shared_scripts{
    '@ox_lib/init.lua',
    "config.lua",
}

client_script 'scripts/cl_*.lua'
server_scripts {
    'scripts/sv_*.lua',
    'interface.lua',
}

server_exports {
    'AddProspectingTarget', -- x, y, z, data
    'AddProspectingTargets', -- list
    'StartProspecting', -- player
    'StopProspecting', -- player
    'IsProspecting', -- player
    'SetDifficulty', -- modifier
}
