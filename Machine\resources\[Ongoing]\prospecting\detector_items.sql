-- SQL untuk menambahkan detector items ke database
-- Jalankan query ini di database server Anda

-- Untuk ESX (items table)
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('detector_level1', 'Basic Metal Detector', 1, 0, 1),
('detector_level2', 'Advanced Metal Detector', 1, 0, 1),
('detector_level3', 'Professional Metal Detector', 1, 0, 1),
('detector_level4', 'Expert Metal Detector', 1, 0, 1),
('detector_level5', 'Master Metal Detector', 1, 0, 1);

-- Untuk QBCore (qb-core/shared/items.lua)
-- Tambahkan ini ke file qb-core/shared/items.lua:

--[[
['detector_level1'] = {
    ['name'] = 'detector_level1',
    ['label'] = 'Basic Metal Detector',
    ['weight'] = 1000,
    ['type'] = 'item',
    ['image'] = 'detector_level1.png',
    ['unique'] = false,
    ['useable'] = true,
    ['shouldClose'] = true,
    ['combinable'] = nil,
    ['description'] = 'A basic metal detector for finding buried treasures'
},
['detector_level2'] = {
    ['name'] = 'detector_level2',
    ['label'] = 'Advanced Metal Detector',
    ['weight'] = 1000,
    ['type'] = 'item',
    ['image'] = 'detector_level2.png',
    ['unique'] = false,
    ['useable'] = true,
    ['shouldClose'] = true,
    ['combinable'] = nil,
    ['description'] = 'An advanced metal detector with better sensitivity'
},
['detector_level3'] = {
    ['name'] = 'detector_level3',
    ['label'] = 'Professional Metal Detector',
    ['weight'] = 1000,
    ['type'] = 'item',
    ['image'] = 'detector_level3.png',
    ['unique'] = false,
    ['useable'] = true,
    ['shouldClose'] = true,
    ['combinable'] = nil,
    ['description'] = 'A professional grade metal detector'
},
['detector_level4'] = {
    ['name'] = 'detector_level4',
    ['label'] = 'Expert Metal Detector',
    ['weight'] = 1000,
    ['type'] = 'item',
    ['image'] = 'detector_level4.png',
    ['unique'] = false,
    ['useable'] = true,
    ['shouldClose'] = true,
    ['combinable'] = nil,
    ['description'] = 'An expert level metal detector with premium features'
},
['detector_level5'] = {
    ['name'] = 'detector_level5',
    ['label'] = 'Master Metal Detector',
    ['weight'] = 1000,
    ['type'] = 'item',
    ['image'] = 'detector_level5.png',
    ['unique'] = false,
    ['useable'] = true,
    ['shouldClose'] = true,
    ['combinable'] = nil,
    ['description'] = 'The ultimate metal detector for master prospectors'
},
--]]

-- Items reward yang mungkin diperlukan (opsional)
-- Tambahkan jika item-item ini belum ada di database Anda

INSERT IGNORE INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('steel', 'Steel', 1, 0, 1),
('copper', 'Copper', 1, 0, 1),
('silver', 'Silver', 1, 0, 1),
('gold', 'Gold', 1, 0, 1),
('aluminum', 'Aluminum', 1, 0, 1),
('diamond', 'Diamond', 1, 0, 1),
('emerald', 'Emerald', 1, 0, 1),
('ruby', 'Ruby', 1, 0, 1),
('sapphire', 'Sapphire', 1, 0, 1),
('rare_gem', 'Rare Gem', 1, 0, 1),
('legendary_artifact', 'Legendary Artifact', 1, 0, 1),
('ancient_coin', 'Ancient Coin', 1, 0, 1);
