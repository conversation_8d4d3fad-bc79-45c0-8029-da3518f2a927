-- SQL untuk menambahkan detector items ke database ESX
-- Jalankan query ini di database ESX Anda

INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES 
('detector_level1', 'Basic Detector', 1, 0, 1),
('detector_level2', 'Advanced Detector', 1, 0, 1),
('detector_level3', 'Professional Detector', 1, 0, 1),
('detector_level4', 'Expert Detector', 1, 0, 1),
('detector_level5', 'Master Detector', 1, 0, 1);

-- Untuk QBCore, tambahkan items berikut ke shared/items.lua:
/*
['detector_level1'] = {['name'] = 'detector_level1', ['label'] = 'Basic Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Basic metal detector for prospecting'},
['detector_level2'] = {['name'] = 'detector_level2', ['label'] = 'Advanced Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Advanced metal detector with better rewards'},
['detector_level3'] = {['name'] = 'detector_level3', ['label'] = 'Professional Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Professional grade detector'},
['detector_level4'] = {['name'] = 'detector_level4', ['label'] = 'Expert Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Expert level detector with great rewards'},
['detector_level5'] = {['name'] = 'detector_level5', ['label'] = 'Master Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Master level detector with the best rewards'},
*/
