-----------------For support, scripts, and more----------------
--------------- https://discord.gg/VGYkkAYVv2  -------------
---------------------------------------------------------------

Config = {}

Config.Core = "ESX" -- ESX or QBCore
Config.PlayerLoadedEvent = "esx:playerLoaded" -- esx:playerLoaded || QBCore:Client:OnPlayerLoaded

Config.ShowBlip = true -- show blip on map

Config.Chances = {
    ["common"] = 100, -- 100%
    ["rare"] = 15, -- 15%
    ["epic"] = 5, -- 5%
}
Config.ShowDrawMaker = true -- show draw marker on in game while prospecting
Config.DetectorItem = "detector"

Config.Zones = {
    [1] = {coords = vector3(2084.5142, 3802.7686, 31.8107), data = "loc1", zoneSize = 100, zoneLocations = 200},
}

Config.DefaultItems = {
    [1] = {name = "steel", min = 1, max = 2}
} -- will be selected if you dont put the common, rare and epic items in the config

Config.Items = {
    ["loc1"] = {
        ["common"] = {
            [1] = {name = "money", min = 5, max = 10},
            [2] = {name = "money", min = 5, max = 10},
        },
        ["rare"] = {
            [1] = {name = "phone", min = 1, max = 1},
        },
        ["epic"] = {
            [1] = {name = "iphone", min = 1, max = 1},
        }
    },
    ["loc2"] = {
        ["common"] = {
            [1] = {name = "money", min = 5, max = 10},
            [2] = {name = "money", min = 5, max = 10},
        },
        ["rare"] = {
            [1] = {name = "phone", min = 1, max = 1},
        },
        ["epic"] = {
            [1] = {name = "iphone", min = 1, max = 1},
        }
    },
}
