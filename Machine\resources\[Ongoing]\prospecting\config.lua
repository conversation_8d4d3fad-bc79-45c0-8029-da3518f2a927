-----------------For support, scripts, and more----------------
--------------- https://discord.gg/VGYkkAYVv2  -------------
---------------------------------------------------------------

Config = {}

Config.Core = "ESX" -- ESX or QBCore
Config.PlayerLoadedEvent = "esx:playerLoaded" -- esx:playerLoaded || QBCore:Client:OnPlayerLoaded

Config.ShowBlip = true -- show blip on map

Config.Chances = {
    ["common"] = 20, -- 100%
    ["rare"] = 20, -- 15%
    ["epic"] = 20, -- 5%
}
Config.ShowDrawMaker = true -- show draw marker on in game while prospecting

-- Detector Items dengan 5 level berbeda
Config.DetectorItems = {
    ["detector1"] = {level = 1, name = "Basic Metal Detector"},
    ["detector2"] = {level = 2, name = "Advanced Metal Detector"},
    ["detector3"] = {level = 3, name = "Professional Metal Detector"},
    ["detector4"] = {level = 4, name = "Expert Metal Detector"},
    ["detector5"] = {level = 5, name = "Master Metal Detector"}
}

Config.Zones = {
    [1] = {coords = vector3(2084.5142, 3802.7686, 31.8107), data = "loc1", zoneSize = 100, zoneLocations = 200},
}

Config.DefaultItems = {
    [1] = {name = "steel", min = 1, max = 2}
} -- will be selected if you dont put the common, rare and epic items in the config

-- Reward berdasarkan level detector (1-5)
Config.Items = {
    ["loc1"] = {
        -- Level 1 Detector Rewards (Basic)
        [1] = {
            ["common"] = {
                [1] = {name = "money", min = 10, max = 20},
                [2] = {name = "steel", min = 1, max = 2},
            },
            ["rare"] = {
                [1] = {name = "copper", min = 1, max = 2},
            },
            ["epic"] = {
                [1] = {name = "silver", min = 1, max = 1},
            }
        },
        -- Level 2 Detector Rewards (Advanced)
        [2] = {
            ["common"] = {
                [1] = {name = "money", min = 25, max = 40},
                [2] = {name = "steel", min = 2, max = 3},
                [3] = {name = "copper", min = 1, max = 2},
            },
            ["rare"] = {
                [1] = {name = "silver", min = 1, max = 2},
                [2] = {name = "aluminum", min = 1, max = 2},
            },
            ["epic"] = {
                [1] = {name = "gold", min = 1, max = 1},
            }
        },
        -- Level 3 Detector Rewards (Professional)
        [3] = {
            ["common"] = {
                [1] = {name = "money", min = 50, max = 75},
                [2] = {name = "copper", min = 2, max = 4},
                [3] = {name = "silver", min = 1, max = 2},
            },
            ["rare"] = {
                [1] = {name = "gold", min = 1, max = 2},
                [2] = {name = "aluminum", min = 2, max = 3},
            },
            ["epic"] = {
                [1] = {name = "diamond", min = 1, max = 1},
                [2] = {name = "emerald", min = 1, max = 1},
            }
        },
        -- Level 4 Detector Rewards (Expert)
        [4] = {
            ["common"] = {
                [1] = {name = "money", min = 100, max = 150},
                [2] = {name = "silver", min = 2, max = 4},
                [3] = {name = "gold", min = 1, max = 2},
            },
            ["rare"] = {
                [1] = {name = "diamond", min = 1, max = 2},
                [2] = {name = "emerald", min = 1, max = 2},
                [3] = {name = "ruby", min = 1, max = 1},
            },
            ["epic"] = {
                [1] = {name = "sapphire", min = 1, max = 1},
                [2] = {name = "rare_gem", min = 1, max = 1},
            }
        },
        -- Level 5 Detector Rewards (Master)
        [5] = {
            ["common"] = {
                [1] = {name = "money", min = 200, max = 300},
                [2] = {name = "gold", min = 2, max = 4},
                [3] = {name = "diamond", min = 1, max = 2},
            },
            ["rare"] = {
                [1] = {name = "emerald", min = 2, max = 3},
                [2] = {name = "ruby", min = 1, max = 2},
                [3] = {name = "sapphire", min = 1, max = 2},
            },
            ["epic"] = {
                [1] = {name = "rare_gem", min = 1, max = 2},
                [2] = {name = "legendary_artifact", min = 1, max = 1},
                [3] = {name = "ancient_coin", min = 1, max = 1},
            }
        }
    },
}
