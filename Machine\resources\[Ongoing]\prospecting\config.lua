-----------------For support, scripts, and more----------------
--------------- https://discord.gg/VGYkkAYVv2  -------------
---------------------------------------------------------------

Config = {}

Config.Core = "ESX" -- ESX or QBCore
Config.PlayerLoadedEvent = "esx:playerLoaded" -- esx:playerLoaded || QBCore:Client:OnPlayerLoaded

Config.ShowBlip = true -- show blip on map

Config.Chances = {
    ["common"] = 20, -- 100%
    ["rare"] = 20, -- 15%
    ["epic"] = 20, -- 5%
}
Config.ShowDrawMaker = true -- show draw marker on in game while prospecting
-- Detector items dengan level berbeda
Config.DetectorItems = {
    ["detector1"] = {level = 1, name = "Basic Detector"},
    ["detector2"] = {level = 2, name = "Advanced Detector"},
    ["detector3"] = {level = 3, name = "Professional Detector"},
    ["detector4"] = {level = 4, name = "Expert Detector"},
    ["detector5"] = {level = 5, name = "Master Detector"}
}

Config.Zones = {
    [1] = {coords = vector3(2084.5142, 3802.7686, 31.8107), data = "loc1", zoneSize = 200, zoneLocations = 200},
}

Config.DefaultItems = {
    [1] = {name = "paku", min = 1, max = 2}
} -- will be selected if you dont put the common, rare and epic items in the config

-- Items berdasarkan level detector
Config.Items = {
    ["loc1"] = {
        -- Level 1 Detector - Basic rewards
        ["level1"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
            },
            ["rare"] = {

            },
            ["epic"] = {

            }
        },
        ["level2"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
                [6] = {name = "old coin", min = 1, max = 3},
            },
            ["rare"] = {

            },
            ["epic"] = {

            }
        },
        -- Level 3 Detector - Good rewards
        ["level3"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
                [6] = {name = "old coin", min = 1, max = 3},
            },
            ["rare"] = {
                [1] = {name = "bp_vest", min = 1, max = 1},
                [2] = {name = "bp_de", min = 1, max = 1},
                [3] = {name = "bp_tec9", min = 1, max = 1},
                [4] = {name = "bp_minismg", min = 1, max = 1},
                [5] = {name = "silverring", min = 1, max = 1},
                [6] = {name = "silverwacth", min = 1, max = 1},
                [7] = {name = "rarecoin", min = 1, max = 3},
            },
            ["epic"] = {

            }
        },
        -- Level 4 Detector - Great rewards
        ["level4"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
                [6] = {name = "old coin", min = 1, max = 3},
            },
            ["rare"] = {
                [1] = {name = "bp_vest", min = 1, max = 1},
                [2] = {name = "bp_de", min = 1, max = 1},
                [3] = {name = "bp_tec9", min = 1, max = 1},
                [4] = {name = "bp_minismg", min = 1, max = 1},
                [5] = {name = "silverring", min = 1, max = 1},
                [6] = {name = "silverwacth", min = 1, max = 1},
                [7] = {name = "rarecoin", min = 1, max = 3},
            },
            ["epic"] = {
                [1] = {name = "goldnecklane", min = 1, max = 1},
                [2] = {name = "ancientrelic", min = 1, max = 1},
                [3] = {name = "refinedmetal", min = 1, max = 1},
            }
        },
        -- Level 5 Detector - Best rewards
        ["level5"] = {
            ["common"] = {
                [1] = {name = "paku", min = 100, max = 100},
                [2] = {name = "tutupbotol", min = 50, max = 50},
                [3] = {name = "besikarat", min = 250, max = 250},
                [4] = {name = "smallmetal", min = 1, max = 3},
                [5] = {name = "rustycoin", min = 1, max = 3},
                [6] = {name = "oldwwwwwwww coin", min = 1, max = 3},
            },
            ["rare"] = {
                [1] = {name = "bp_vest", min = 1, max = 1},
                [2] = {name = "bp_wde", min = 1, max = 1},
                [3] = {name = "bp_tec9", min = 1, max = 1},
                [4] = {name = "bp_minismg", min = 1, max = 1},
                [5] = {name = "silverring", min = 1, max = 1},
                [6] = {name = "silverwacth", min = 1, max = 1},
                [7] = {name = "rarecoin", min = 1, max = 3},
            },
            ["epic"] = {
                [1] = {name = "goldnecklane", min = 1, max = 1},
                [2] = {name = "ancientrelic", min = 1, max = 1},
                [3] = {name = "refinedmetal", min = 1, max = 1},
                [4] = {name = "legendarycoin", min = 1, max = 1},
                [4] = {name = "fleca_card", min = 1, max = 1},
                [4] = {name = "dd_card", min = 1, max = 1},
                [4] = {name = "bc_card", min = 1, max = 1},
            }
        }
    },
}
