# Setup Guide - Multi-Level Detector System

## Langkah-langkah Instalasi

### 1. Instalasi Resource
1. Pastikan folder `prospecting` sudah ada di folder resources server Anda
2. Tam<PERSON>kan `ensure prospecting` ke server.cfg
3. Restart server

### 2. Tambahkan Items ke Database/Inventory

#### Untuk ESX:
Jalankan file `detector_items.sql` di database ESX Anda, atau jalankan query berikut:

```sql
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES 
('detector_level1', 'Basic Detector', 1, 0, 1),
('detector_level2', 'Advanced Detector', 1, 0, 1),
('detector_level3', 'Professional Detector', 1, 0, 1),
('detector_level4', 'Expert Detector', 1, 0, 1),
('detector_level5', 'Master Detector', 1, 0, 1);
```

#### Untuk QBCore:
Tambahkan items berikut ke `qb-core/shared/items.lua`:

```lua
['detector_level1'] = {['name'] = 'detector_level1', ['label'] = 'Basic Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Basic metal detector for prospecting'},
['detector_level2'] = {['name'] = 'detector_level2', ['label'] = 'Advanced Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Advanced metal detector with better rewards'},
['detector_level3'] = {['name'] = 'detector_level3', ['label'] = 'Professional Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Professional grade detector'},
['detector_level4'] = {['name'] = 'detector_level4', ['label'] = 'Expert Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Expert level detector with great rewards'},
['detector_level5'] = {['name'] = 'detector_level5', ['label'] = 'Master Detector', ['weight'] = 100, ['type'] = 'item', ['image'] = 'detector.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Master level detector with the best rewards'},
```

### 3. Konfigurasi

#### Mengubah Lokasi Prospecting:
Edit `config.lua` bagian `Config.Zones`:
```lua
Config.Zones = {
    [1] = {coords = vector3(x, y, z), data = "loc1", zoneSize = 100, zoneLocations = 200},
}
```

#### Mengubah Reward Items:
Edit `config.lua` bagian `Config.Items` untuk menyesuaikan reward setiap level detector.

### 4. Testing

1. Berikan salah satu detector item ke player: `/give [id] detector_level1 1`
2. Gunakan detector item dari inventory
3. Pergi ke lokasi prospecting (ditandai di map)
4. Ikuti audio/visual cues untuk menemukan treasure
5. Klik kiri untuk menggali saat menemukan spot

### 5. Commands untuk Testing (Opsional)

Tambahkan commands berikut untuk testing:

```lua
-- Untuk ESX
RegisterCommand('givedetector', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    local level = args[1] or "1"
    xPlayer.addInventoryItem('detector_level'..level, 1)
end, false)

-- Untuk QBCore  
RegisterCommand('givedetector', function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    local level = args[1] or "1"
    Player.Functions.AddItem('detector_level'..level, 1)
end, false)
```

## Cara Kerja System

1. **Multi-Level Detection**: System otomatis mendeteksi detector level tertinggi di inventory player
2. **Progressive Rewards**: Setiap level memberikan reward yang lebih baik
3. **Single Location**: Semua detector bekerja di lokasi yang sama (1 zone dengan 200 spots)
4. **Automatic Scaling**: Reward otomatis disesuaikan berdasarkan level detector yang digunakan

## Troubleshooting

### Player tidak bisa mulai prospecting:
- Pastikan player memiliki salah satu detector item
- Check console untuk error messages
- Pastikan semua detector items sudah terdaftar di database/items.lua

### Reward tidak sesuai level:
- Check function `GetPlayerDetectorLevel()` di sv_prospecting.lua
- Pastikan Config.Items memiliki konfigurasi untuk semua level

### Error saat menggunakan detector:
- Pastikan semua detector items terdaftar sebagai useable items
- Check server console untuk error messages
