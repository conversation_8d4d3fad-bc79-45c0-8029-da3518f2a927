# Setup Guide - Prospecting Multi-Level Detector System

## ✅ Fitur yang Sudah Diimplementasi
- **1 Lokasi Prospecting**: Semua aktivitas di satu area
- **5 Level Detector**: detector_level1 sampai detector_level5
- **Progressive Rewards**: Level tinggi = reward lebih bagus
- **Sistem Sederhana**: Kembali ke kode awal tanpa optimasi kompleks

## 🚀 Instalasi Cepat

### 1. Database Setup
Jalankan file `detector_items.sql`:
```sql
-- ESX Database
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('detector_level1', 'Basic Metal Detector', 1, 0, 1),
('detector_level2', 'Advanced Metal Detector', 1, 0, 1),
('detector_level3', 'Professional Metal Detector', 1, 0, 1),
('detector_level4', 'Expert Metal Detector', 1, 0, 1),
('detector_level5', 'Master Metal Detector', 1, 0, 1);
```

### 2. QBCore Setup
Tambahkan items ke `qb-core/shared/items.lua` (lihat detector_items.sql)

### 3. Start Resource
```
start prospecting
```

## 💎 Sistem Reward

### Level Detector & Rewards:

| Level | Detector | Money | Common Items | Rare Items | Epic Items |
|-------|----------|-------|--------------|------------|------------|
| **1** | Basic | 10-20 | Steel, Copper | Copper | Silver |
| **2** | Advanced | 25-40 | Steel, Copper | Silver, Aluminum | Gold |
| **3** | Professional | 50-75 | Copper, Silver | Gold, Aluminum | Diamond, Emerald |
| **4** | Expert | 100-150 | Silver, Gold | Diamond, Emerald, Ruby | Sapphire, Rare Gem |
| **5** | Master | 200-300 | Gold, Diamond | Emerald, Ruby, Sapphire | Rare Gem, Legendary Artifact, Ancient Coin |

## 🎮 Cara Bermain

1. **Dapatkan Detector**: Player butuh detector level 1-5
2. **Gunakan Detector**: Klik kanan atau tekan tombol use
3. **Pergi ke Lokasi**: Lihat blip di map (koordinat: 2084.5142, 3802.7686, 31.8107)
4. **Cari Target**: Gunakan detector untuk scan area
5. **Gali**: Tekan E saat dekat dengan target
6. **Dapatkan Reward**: Reward tergantung level detector tertinggi yang dimiliki

## 🧪 Testing Commands

```
/give detector_level1 1
/give detector_level2 1
/give detector_level3 1
/give detector_level4 1
/give detector_level5 1
```

## ⚙️ Konfigurasi

Edit `config.lua` untuk menyesuaikan:
- Reward items dan jumlah per level
- Lokasi prospecting zone
- Chances untuk rare/epic items
- Zone size dan jumlah spots

## 🔧 Troubleshooting

**Player tidak bisa mulai prospecting:**
- Pastikan player memiliki minimal detector_level1
- Check server console untuk error
- Pastikan items sudah ada di database

**Reward tidak sesuai level:**
- Check function GetPlayerDetectorLevel() di server script
- Pastikan Config.Items memiliki data untuk semua level 1-5

**Error saat menggunakan detector:**
- Pastikan semua detector_level1-5 terdaftar sebagai useable items
- Check server console untuk error messages

## 📍 Lokasi & Specs

- **Koordinat**: 2084.5142, 3802.7686, 31.8107
- **Zone Radius**: 100 meter
- **Total Spots**: 200 lokasi prospecting
- **Respawn**: Otomatis setelah diambil
